This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
.gitignore
app.py
chainlit.md
main.py
README.md
requirements.txt
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="app.py">
from fastapi import FastAPI
from chainlit.utils import mount_chainlit

app = FastAPI()


@app.get("/main")
def read_main():
    return {"message": "CMO ChainLit API"}

mount_chainlit(app=app, target="main.py", path="/chat")
</file>

<file path="chainlit.md">
<img src="\public\logo_light.png" alt="IFCC Logo" width="100"/>

# Welcome to the Clinical Metabolomics Oracle!

The Clinical Metabolomics Oracle (CMO) is an artificial intelligence tool designed to answer questions about clinical metabolomics and clinical chemistry.  
The CMO uses information from scientific articles, protocol documents, and reports to supplement its training. 📚

*Results are not intended to replace the advice of a qualified professional. Content generated by the Clinical Metabolomics Oracle is for informational purposes only.*  

[The International Federation of Clinical Chemistry and Laboratory Medicine](https://ifcc.org/)
</file>

<file path="main.py">
from copy import deepcopy
import os

import chainlit as cl
from openai import AsyncOpenAI, AsyncAssistantEventHandler
from typing_extensions import override

# Load OPENAI_API_KEY from .env file
from dotenv import load_dotenv
load_dotenv()

# set up ChatGPT API
client = AsyncOpenAI(
    api_key = os.getenv("OPENAI_API_KEY"),
)
cl.instrument_openai()

#####################################################################################
# Assistant ids can be found here: https://platform.openai.com/playground/assistants
assistant_id = os.getenv("CMO_ASSISTANT_ID")
#####################################################################################

# set name in browser window
cl.config.ui.name = "CMO"

FILEINFO = {}
async def get_files():
    # get vector store file ids and file names from OpenAI
    ids = await client.files.list()
    for file in ids.data:
        FILEINFO[file.id] = {"name":file.filename, "sources":[]}


class EventHandler(AsyncAssistantEventHandler): 
  def __init__(self):
    super().__init__()
    self.current_message = cl.Message(content="")

  @override
  async def on_text_created(self, text) -> None:
    await self.current_message.send()
      
  @override
  async def on_text_delta(self, delta, snapshot):
    await self.current_message.stream_token(delta.value)


@cl.on_chat_start
async def on_chat_start(accepted: bool = False):
    # SET UP RAG PIPELINE HERE!
    
    # get vector store file info for building citations
    await get_files()

    subject = "Clinical Metabolomics Oracle"
    # display welcome and disclaimer
    descr = f"Welcome to the {subject}."
    #subhead = "Click on the menu button in the text area to select a subject.\n"
    disclaimer = f"{subject} is an automated question answering tool, and is not intended to replace the advice of a qualified professional.\nContent generated by {subject} is for informational purposes only."
    elements = [
        #cl.Text(name=descr, content=subhead, display="inline"),
        cl.Text(name="Disclaimer", content=disclaimer, display="inline")
    ]
    await cl.Message(
        content='',
        elements=elements,
    ).send()

    res = {}
    # set up messages
    denied_msg = cl.Message(content = "You must agree to the terms of service to continue.")
    disclaimer_msg = cl.AskActionMessage(
            content = f"Do you understand the purpose and limitations of {subject}?",
            actions = [
                cl.Action(name="I understand", value="continue", label="I Understand", description="Agree and continue"),
                cl.Action(name="Disagree", value="disagree", label="Disagree", description="Disagree to terms of service")
            ],
            timeout = 600  # five minutes
        )
    # continue prompting until user selects 'I understand'
    while not accepted:
        res = await disclaimer_msg.send()

        accepted = res.get("value") == "continue"
        if not accepted:
            await denied_msg.send()
    
    # clean up
    await disclaimer_msg.remove()
    await denied_msg.remove()
    welcome = f"Welcome!\nAsk me a question about clinical metabolomics to get started."
    await cl.Message(content=welcome).send()

    # create a thread
    thread = await client.beta.threads.create()
    cl.user_session.set("thread_id", thread.id)



@cl.on_message
async def on_message(message: cl.Message):
    '''
    Sends user query to OpenAI, streams response.
    '''
    thread_id = cl.user_session.get("thread_id")

    # add message to thread
    user_msg = await client.beta.threads.messages.create(
        thread_id=thread_id,
        role="user",
        content=message.content,
    )
    # thinking message
    thinking_msg = cl.Message(content="Thinking...")
    await thinking_msg.send()

    # create and stream a run
    async with client.beta.threads.runs.stream(
        thread_id=thread_id,
        assistant_id=assistant_id,
        event_handler=EventHandler(),
    ) as stream:
        await stream.until_done()

    await thinking_msg.remove()
    # append citations
    messages = await client.beta.threads.messages.list(
    thread_id=thread_id
    )
    citation_file_info = deepcopy(FILEINFO)
    citation = messages.data[0].content[0].text
    for c in citation.annotations:
        file_id = c.file_citation.file_id
        citation_file_info[file_id]["sources"].append(c.text)
    # display sources
    sources = f"**SOURCES**\n"
    source_count = 0
    for file in citation_file_info:
        if len(citation_file_info[file]["sources"]) > 0:
            for source in set(citation_file_info[file]["sources"]):
                temp = citation_file_info[file]['name'][:-4]
                sources += f"{source}: {temp.replace('$','/')}\n"     
                source_count += 1
    if source_count > 0:     
        await cl.Message(content=sources).send()
</file>

<file path="README.md">
# ChainLit - FastAPI Production Setup with Gunicorn and Nginx

ChainLit is a predictive analytics tool. Please follow these steps to set up a new server or use an existing one for production.

## Prerequisites

- nginx
- Root or sudo access on your server
- Python 3.11.9 with miniconda
- gunicorn
- Ensure to check the latest documentation on setting up web application servers: `https://wiki.wishartlab.com/wiki/index.php/Web_Application_Setup`

## Step 1: Prepare Your FastAPI Application

### Configuration Adjustments and Development Guidelines

- Production `ROOT` folder: `/apps/shchat/project/current`
- Always pull the latest `main` branch into the production `ROOT` folder
- Install requirements.txt via `pip install -r requirements.txt`
- FastAPI App is mounting this chainlit app on `/chat`. Check `main.py`
- Check `/apps/shchat/project/shared/config/.env` for production environment variables
- Review production logs in `/apps/shchat/project/shared/logs`

## Step 2: Install and Configure Gunicorn

### Install Gunicorn, chainlit and openai

Install all required packages within your project's miniconda virtual environment:
```bash
pip install -r requirements.txt
```

### Configure Systemd for Gunicorn

#### Gunicorn Socket
Configure the Gunicorn socket in `/etc/systemd/system/gunicorn.socket`:
```ini
[Unit]
Description=gunicorn socket for shchat

[Socket]
ListenStream=/run/gunicorn.sock

[Install]
WantedBy=sockets.target
```

#### Gunicorn Service
Configure the Gunicorn service in `/etc/systemd/system/gunicorn.service`:
```ini
[Unit]
Description=gunicorn daemon for shchat
Requires=gunicorn.socket
After=network.target

[Service]
User=shchat
Group=shchat
WorkingDirectory=/apps/shchat/project/current
ExecStart=/apps/shchat/miniconda3/envs/shchat/bin/gunicorn \
          -k uvicorn.workers.UvicornWorker \
          --access-logfile /apps/shchat/project/shared/logs/access.log \
          --error-logfile /apps/shchat/project/shared/logs/error.log \
          --workers 1 \
          --bind unix:/run/gunicorn.sock \
          app:app

[Install]
WantedBy=multi-user.target
```

### Start and Enable Gunicorn Services and Socket
```bash
sudo systemctl start gunicorn.socket
sudo systemctl enable gunicorn.socket

sudo systemctl start gunicorn.service
sudo systemctl enable gunicorn.service
```

## Step 3: Install and Configure Nginx

### Install Nginx
```bash
sudo apt update
sudo apt install nginx
```

### Configure Nginx for ChainLit

Configure Nginx to serve the ChainLit application by editing the configuration file at `/etc/nginx/sites-available/shchat` and linking it to `sites-enabled`:
```nginx
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name subdomain.example.com;

    ssl_certificate /etc/ssl/certs/example.pem;
    ssl_certificate_key /etc/ssl/private/example.key;
    ssl_session_cache shared:SSL:10m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    root /apps/shchat/project/current/public;

    location / {
        try_files $uri @proxy;
    }

    location @proxy {
        proxy_pass http://unix:/run/gunicorn.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    access_log /var/log/nginx/project_access.log;
    error_log /var/log/nginx/project_error.log;
}

server {
    listen 80;
    listen [::]:80;
    server_name subdomain.example.com;
    return 301 https://$host$request_uri;
}
```

### Enable the Nginx Site Configuration
```bash
sudo ln -s /etc/nginx/sites-available/shchat /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Allow Traffic via Firewall
```bash
sudo ufw allow 'Nginx Full'
```

## Step 4: Final Checks and Debugging

- **Verify Gunicorn and Nginx Status**:
  Check the status to ensure no errors:
  ```bash
  sudo systemctl status gunicorn
  sudo systemctl status nginx
  ```

- **Review Logs**:
  Inspect logs for errors:
  - `/var/log/nginx/project_error.log`
  - `/var/log/nginx/project_access.log`
  - `/apps/shchat/project/shared/logs/access.log`
  - `/apps/shchat/project/shared/logs/error.log`

## Step 5: Deployment Procedure

- **Update Code and Configurations**:
  Regularly pull updates and ensure configurations are up to date.
  ```bash
  cd /apps/shchat/project/current
  git pull origin main
  pip install -r requirements.txt
  ```

### To Run it locally:

```bash
uvicorn app:app --host 0.0.0.0 --port 8000
```
</file>

<file path="requirements.txt">
aiofiles==23.2.1
annotated-types==0.7.0
anyio==3.7.1
asyncer==0.0.2
beautifulsoup4==4.12.3
bidict==0.23.1
certifi==2024.7.4
chainlit==1.1.306
charset-normalizer==3.3.2
chevron==0.14.0
click==8.1.7
dataclasses-json==0.5.14
Deprecated==1.2.14
distro==1.9.0
fastapi==0.110.3
filetype==1.2.0
googleapis-common-protos==1.63.2
grpcio==1.65.1
gunicorn==22.0.0
h11==0.14.0
httpcore==1.0.5
httpx==0.27.0
idna==3.7
importlib_metadata==8.0.0
Lazify==0.4.0
literalai==0.0.607
marshmallow==3.21.3
mypy-extensions==1.0.0
nest-asyncio==1.6.0
numpy==1.26.0
openai==1.37.0
opentelemetry-api==1.26.0
opentelemetry-exporter-otlp==1.26.0
opentelemetry-exporter-otlp-proto-common==1.26.0
opentelemetry-exporter-otlp-proto-grpc==1.26.0
opentelemetry-exporter-otlp-proto-http==1.26.0
opentelemetry-instrumentation==0.47b0
opentelemetry-proto==1.26.0
opentelemetry-sdk==1.26.0
opentelemetry-semantic-conventions==0.47b0
packaging==23.2
protobuf==4.25.4
pydantic==2.8.2
pydantic_core==2.20.1
PyJWT==2.8.0
python-dotenv==1.0.1
python-engineio==4.9.1
python-multipart==0.0.9
python-socketio==5.11.3
requests==2.32.3
simple-websocket==1.0.0
sniffio==1.3.1
soupsieve==2.5
starlette==0.37.2
syncer==2.0.3
tomli==2.0.1
tqdm==4.66.4
typing-inspect==0.9.0
typing_extensions==4.12.2
uptrace==1.26.0
urllib3==2.2.2
uvicorn==0.25.0
watchfiles==0.20.0
wrapt==1.16.0
wsproto==1.2.0
zipp==3.19.2
</file>

<file path=".gitignore">
# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Environment file
.env
.files
# .chainlit

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv
</file>

</files>
