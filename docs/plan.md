

# **Development and Architecture Plan: Evolving the Clinical Metabolomics Oracle**

## **Executive Summary**

This document presents a comprehensive, two-stage development plan to evolve the "Clinical Metabolomics Oracle" (CMO) from its current proof-of-concept implementation into a production-grade, advanced reasoning system. The initial application, built on Chainlit and a single OpenAI Assistant, will be systematically refactored into a modern, decoupled architecture featuring a pure FastAPI API backend and a React frontend client.

**Stage 1, the Minimum Viable Product (MVP)**, focuses on establishing this new architectural foundation. Key deliverables include a secure, multi-role user and account management system with a robust JWT authentication scheme; a fully internationalized user interface; a sophisticated chat interface supporting real-time streaming responses; and persistent chat history. This stage will lay the groundwork for future expansion by deploying a scalable, containerized application with a full CI/CD pipeline.

**Stage 2, Expansion with Advanced Data Sources**, transforms the CMO into a true scientific reasoning engine. This stage introduces a backend Orchestration Layer, the system's cognitive core, designed to intelligently plan and execute queries across multiple, heterogeneous data sources. The system will learn to synthesize information from:

1. A vector database (Qdrant) for traditional Retrieval-Augmented Generation (RAG) on ingested scientific literature.  
2. A Neo4j Knowledge Graph for structured GraphRAG on complex metabolic pathways.  
3. The Perplexity API for live web searches on cutting-edge research.  
4. The original OpenAI Assistant as a foundational knowledge base.

This stage also includes the development of a secure administrative panel, empowering administrators to manage users, data sources, and the configuration of the Orchestration Layer. The final deliverable is a highly advanced, transparent, and configurable AI system tailored specifically for the complex domain of clinical metabolomics. This plan provides an actionable, technically detailed blueprint for the development team, covering technology selection, data modeling, implementation strategies, testing protocols, and deployment automation.

---

## **Stage 1: Building the Minimum Viable Product (MVP)**

The primary objective of Stage 1 is to transition from the monolithic proof-of-concept to a robust, scalable, and maintainable production architecture. This involves a complete refactor into a decoupled system, the implementation of core user-facing features, and the establishment of automated development and deployment workflows. By the end of this stage, a functional MVP will be live, providing a solid foundation for the advanced features planned in Stage 2\.

### **Phase 1: Backend Foundations: API, Database, and Authentication**

**Objective:** To construct a secure, asynchronous, and scalable FastAPI backend. This phase will establish a production-ready PostgreSQL database with version-controlled schema migrations and implement a comprehensive, secure authentication system using JSON Web Tokens (JWTs) featuring refresh token capabilities and a server-side revocation mechanism.

**Key Technologies:** FastAPI, PostgreSQL, SQLAlchemy (with Asyncio), SQLModel, Alembic, Pydantic, Passlib, python-jose, Redis.

#### **Project Scaffolding and Configuration**

The project will be structured to promote modularity and maintainability, separating concerns such as application logic, data models, API schemas, and authentication utilities. A standard Python virtual environment will be used for dependency management.1

Configuration will be managed through Pydantic's BaseSettings, which allows for type-validated settings to be loaded from environment variables. This is a critical security practice, ensuring that sensitive information like database credentials and JWT secrets are not hardcoded in the application source.2 The project structure will be organized as follows:

cmo\_backend/  
├── alembic/              \# Alembic migration scripts  
├── app/  
│   ├── \_\_init\_\_.py  
│   ├── api/              \# API router modules (users, chat, admin)  
│   ├── core/             \# Core logic, configuration, security  
│   ├── crud/             \# Database create, read, update, delete operations  
│   ├── models/           \# SQLModel data models  
│   ├── schemas/          \# Pydantic schemas (if not using SQLModel directly)  
│   └── main.py           \# Main FastAPI application instance  
├── tests/                \# Pytest tests  
├──.env                  \# Environment variables (not committed to git)  
├── alembic.ini           \# Alembic configuration  
├── pyproject.toml        \# Project metadata and dependencies (if using Poetry)  
└── requirements.txt      \# Project dependencies

#### **Asynchronous Database Integration**

For a high-throughput, real-time application like a chatbot, preventing I/O blocking is paramount. Therefore, all database interactions will be asynchronous.

* **Database Selection:** PostgreSQL is selected as the relational database due to its robustness, performance, and excellent support within the Python ecosystem for advanced features.3  
* **Asynchronous ORM:** The backend will utilize SQLAlchemy's asyncio extension in conjunction with the asyncpg driver, which is the recommended high-performance stack for asynchronous PostgreSQL operations with Python.4  
* **Session Management:** A dedicated session manager class will be implemented to handle the lifecycle of the create\_async\_engine and async\_sessionmaker. This manager will provide a clean, reusable database session via a dependency injection function (get\_db), ensuring that each API request receives an isolated session that is properly closed afterward. This pattern centralizes database connection logic and improves code maintainability.4  
* **Schema Migrations:** Database schema changes will be managed using Alembic. This is a non-negotiable requirement for any production system, as it provides version control for the database schema, allowing for safe, iterative, and reversible updates. The Alembic environment will be specifically configured to work with the asynchronous SQLAlchemy engine and to recognize the SQLModel table metadata.6

#### **Data Modeling with SQLModel and Pydantic**

To streamline development and reduce code duplication, this project will adopt SQLModel. SQLModel elegantly combines SQLAlchemy's ORM capabilities with Pydantic's validation schemas into single, cohesive class definitions. This means a single model definition can serve as the database table schema, the API request body validator, and the API response model, minimizing the chance of discrepancies between layers.3

The core data models for the MVP are:

* **User Model:**  
  Python  
  \# app/models/user.py  
  from typing import Optional  
  from sqlmodel import Field, SQLModel

  class UserRole(str, Enum):  
      USER \= "user"  
      ADMIN \= "admin"

  class UserBase(SQLModel):  
      email: str \= Field(unique=True, index=True)  
      preferred\_language: str \= Field(default="en")

  class User(UserBase, table=True):  
      id: Optional\[int\] \= Field(default=None, primary\_key=True)  
      hashed\_password: str  
      role: UserRole \= Field(default=UserRole.USER)

  class UserCreate(UserBase):  
      password: str

  class UserPublic(UserBase):  
      id: int  
      role: UserRole

* **Chat Models:**  
  Python  
  \# app/models/chat.py  
  from typing import Optional, List  
  from datetime import datetime  
  from sqlmodel import Field, SQLModel, Relationship

  class SenderType(str, Enum):  
      USER \= "user"  
      AI \= "ai"

  class ChatMessageBase(SQLModel):  
      content: str  
      sender: SenderType  
      timestamp: datetime \= Field(default\_factory=datetime.utcnow)  
      thread\_id: int \= Field(foreign\_key="chatthread.id")

  class ChatMessage(ChatMessageBase, table=True):  
      id: Optional\[int\] \= Field(default=None, primary\_key=True)  
      thread: "ChatThread" \= Relationship(back\_populates="messages")

  class ChatThread(SQLModel, table=True):  
      id: Optional\[int\] \= Field(default=None, primary\_key=True)  
      user\_id: int \= Field(foreign\_key="user.id")  
      created\_at: datetime \= Field(default\_factory=datetime.utcnow)  
      title: str

      messages: List\[ChatMessage\] \= Relationship(back\_populates="thread")

#### **Production-Grade JWT Authentication System**

A simple JWT implementation is insufficient for a secure production environment. While JWTs offer stateless efficiency, this becomes a liability if a token is compromised, as it remains valid until expiration regardless of user actions like logging out.2 To address this, the CMO will implement a robust authentication system that includes both short-lived access tokens and long-lived refresh tokens, backed by a server-side token revocation mechanism.

* **Token Strategy:**  
  * Upon successful login, the API will issue two tokens: an **access token** (short-lived, e.g., 15 minutes) and a **refresh token** (long-lived, e.g., 7 days).11  
  * The access token is used to authenticate requests to protected API endpoints.  
  * When the access token expires, the client can use the refresh token to silently request a new access token from a dedicated /auth/refresh endpoint without requiring the user to log in again.12  
* **Revocation Mechanism:**  
  * To enable true user logout and immediate revocation of compromised tokens, a Redis-based blocklist will be implemented. Redis is chosen for its high performance in this role.  
  * When a user logs out via the /auth/logout endpoint, the unique identifier (jti claim) of both their access and refresh tokens will be added to a Redis set. These entries in Redis will be set to expire automatically when the original token would have expired, preventing the blocklist from growing indefinitely.13  
  * The core authentication dependency (get\_current\_active\_user) that protects API endpoints will be enhanced. Before validating the token's signature, it will perform a quick check to see if the token's jti exists in the Redis blocklist. If it does, the token is rejected, even if it's not expired. This hybrid approach delivers the performance of stateless JWTs for most requests while providing the security of stateful revocation when needed.  
* **Implementation Details:**  
  * User passwords will be securely hashed using passlib with the bcrypt algorithm.1  
  * The /auth/token endpoint will use FastAPI's OAuth2PasswordRequestForm for standard credential submission.  
  * A suite of dependency functions will be created to protect endpoints based on authentication status and user role (e.g., get\_current\_user, get\_current\_admin\_user), encapsulating the logic for token decoding, signature verification, and the Redis blocklist check.15

#### **Initial API Endpoint Specification**

To enable parallel development between the backend and frontend teams, a formal API contract is essential. The following table defines the endpoints required for the MVP.

| Endpoint | HTTP Method | Description | Required Role | Request Body (Pydantic/SQLModel Schema) | Success Response (Pydantic/SQLModel Schema) |
| :---- | :---- | :---- | :---- | :---- | :---- |
| /auth/register | POST | Creates a new user account. | Public | UserCreate | UserPublic |
| /auth/token | POST | Authenticates a user and returns tokens. | Public | OAuth2PasswordRequestForm | Token (contains access\_token, refresh\_token) |
| /auth/refresh | POST | Issues a new access token using a refresh token. | Authenticated | (Refresh token in Authorization header) | Token (contains new access\_token) |
| /auth/logout | POST | Invalidates the current user's tokens. | Authenticated | (Access token in Authorization header) | {"msg": "Successfully logged out"} |
| /users/me | GET | Retrieves the current authenticated user's profile. | Authenticated | None | UserPublic |
| /users/me | PATCH | Updates the current user's profile (e.g., language). | Authenticated | UserUpdate (subset of fields) | UserPublic |
| /chat | POST | Starts a new chat session with the AI. | Authenticated | ChatMessageCreate | StreamingResponse |
| /chat/history | GET | Retrieves all chat threads for the current user. | Authenticated | None | List |
| /chat/history/{thread\_id} | GET | Retrieves a specific chat thread with all its messages. | Authenticated | None | ChatThread (with messages) |

### **Phase 2: Frontend Foundations: UI Scaffolding and User Experience**

**Objective:** To initialize a modern, high-performance React application using Vite, establish a robust and scalable state management pattern with Redux Toolkit, and implement application-wide routing, including secure protected routes for authenticated sections of the application.

**Key Technologies:** React, Vite, TypeScript, Redux Toolkit, React Router DOM v6, Axios.

#### **Project Initialization with Vite and TypeScript**

The frontend project will be scaffolded using Vite with the react-ts template. Vite is chosen over the older Create React App due to its significantly faster development server startup times and more efficient Hot Module Replacement (HMR), which provides a superior developer experience.16

The project will adopt a structured and scalable folder organization to ensure maintainability as the application grows:

cmo\_frontend/  
├── public/  
│   └── locales/          \# i18n translation files  
├── src/  
│   ├── api/              \# Axios instance and API service definitions  
│   ├── app/              \# Redux store and root component setup  
│   ├── assets/           \# Static assets like images, fonts  
│   ├── components/       \# Reusable, presentational UI components  
│   ├── features/         \# Redux slices (e.g., authSlice, chatSlice)  
│   ├── hooks/            \# Custom React hooks  
│   ├── lib/              \# Utility functions, constants  
│   ├── pages/            \# Top-level page components  
│   ├── styles/           \# Global styles, theme configuration  
│   └── main.tsx          \# Application entry point  
├──.env                  \# Environment variables  
├── index.html            \# Main HTML file  
├── package.json          \# Project dependencies and scripts  
└── vite.config.ts        \# Vite configuration file

#### **API Client and State Management Setup**

* **API Client (Axios):** An Axios instance will be configured to serve as the primary HTTP client. This instance will be pre-configured with the backend's base URL (/api/v1). It will also feature request and response interceptors. The request interceptor will automatically attach the JWT access token from the Redux store to the Authorization header of outgoing requests. The response interceptor will handle 401 Unauthorized errors by automatically triggering the token refresh flow, providing a seamless user experience.  
* **State Management (Redux Toolkit):** The application's state will be managed by Redux Toolkit, the official and modern standard for Redux development. configureStore will be used to set up the Redux store, which automatically includes helpful middleware like Redux Thunk.19 A central  
  auth slice, created with createSlice, will manage the user's authentication state (isAuthenticated), the user profile object, and the access/refresh tokens. This slice will contain reducers to handle actions like loginSuccess and logoutSuccess, updating the state accordingly.

#### **Routing with React Router v6**

Application routing will be handled by React Router v6. A key architectural decision is the use of layout routes to manage protected areas of the application, which is a more elegant and declarative approach than individually wrapping each component.

* **Layout Route Strategy:** Instead of creating a ProtectedRoute component and wrapping every single private route with it 21, a more modern pattern available in React Router v6 will be used. A layout component, say  
  ProtectedRouteLayout, will be created. This component will check the authentication state from the Redux store. If the user is authenticated, it will render the \<Outlet /\> component, which acts as a placeholder for nested child routes. If the user is not authenticated, it will render \<Navigate to="/login" /\>, redirecting them.23  
* **Router Configuration:** The main router, created with createBrowserRouter, will define a parent route that uses ProtectedRouteLayout as its element. All routes that require authentication (/chat, /account, /admin/\*) will be defined as children of this layout route. This approach centralizes the protection logic, making the routing configuration cleaner and easier to manage. A similar, more specific AdminRouteLayout will be nested to protect the /admin/\* paths, performing an additional check for the user's role.

Example router configuration:

TypeScript

// src/app/router.tsx  
import { createBrowserRouter, Navigate } from 'react-router-dom';  
import LoginPage from '../pages/LoginPage';  
import ChatPage from '../pages/ChatPage';  
import AccountPage from '../pages/AccountPage';  
import ProtectedRouteLayout from './ProtectedRouteLayout';  
import AdminRouteLayout from './AdminRouteLayout';  
import AdminDashboard from '../pages/AdminDashboard';

const router \= createBrowserRouter(,  
      },  
    \],  
  },  
\]);

export default router;

### **Phase 3: Core Chat Functionality: Streaming and State Management**

**Objective:** To implement the core chat interface, connecting the React frontend to the FastAPI backend's streaming endpoint. This will provide a real-time, "typing" effect for the AI's responses, powered by the initial OpenAI Assistant.

**Key Technologies:** EventSource API (Server-Sent Events), Redux Toolkit (createAsyncThunk).

#### **Backend: Streaming Endpoint**

The FastAPI backend will expose a /chat endpoint that returns a StreamingResponse. This is an asynchronous generator function that will establish a connection to the OpenAI Assistant's streaming API. As the OpenAI API yields token chunks, the FastAPI endpoint will immediately yield them to the frontend client, wrapped in the Server-Sent Events (SSE) format. This avoids waiting for the entire response to be generated, which is essential for a responsive chat experience.

#### **Frontend: Stream Consumption and State Management**

Handling a real-time stream requires a more nuanced approach than a standard API request-response cycle. A naive attempt to manage the entire stream within a single createAsyncThunk payload creator is problematic because the thunk's promise resolves only once, at the end of the stream. This doesn't align with the need for incremental UI updates as tokens arrive.25

A more robust architecture separates the concerns of request lifecycle management from real-time data consumption:

1. **Request Initiation (Redux Thunk):** A sendMessage async thunk will be created using createAsyncThunk.20 Its primary responsibility is to initiate the chat request. When dispatched, it will:  
   * Dispatch the pending action (e.g., chat/sendMessage/pending), which a reducer will use to set a loading state in the Redux store (e.g., state.status \= 'loading').  
   * Make the initial POST request to the backend's /chat endpoint.  
   * The thunk itself does not wait for the stream to finish. Its job is done once the request is sent.  
2. **Stream Consumption (Component useEffect):** The ChatPage component will contain a useEffect hook that listens for changes in the Redux chat.status.  
   * When the status changes to 'loading', this hook will instantiate a new EventSource object, pointing to the streaming endpoint.  
   * An onmessage event listener will be attached to the EventSource. As each data chunk (a token) arrives from the backend, this listener will dispatch a *new, synchronous* Redux action (e.g., appendAiMessageChunk({ chunk: '...' })).  
   * The reducer for appendAiMessageChunk will find the last message in the state.messages array (which is the AI's in-progress response) and append the new chunk to its content. This is what creates the live typing effect on the screen.  
   * An onerror listener on the EventSource will detect the end of the stream. It will then dispatch a final action to signify the completion of the response (e.g., messageStreamComplete()), which can update the status in the Redux store back to 'idle'.

This architecture correctly uses the async thunk for its intended purpose—managing the lifecycle of an asynchronous operation—while handling the unique, incremental nature of the data stream within the component and synchronous reducers.

#### **UI Implementation**

The ChatPage UI will be built to reactively display the state managed by Redux. It will include:

* A message display area that maps over the chat.messages array from the store, rendering each user and AI message.  
* An input form that dispatches the sendMessage thunk when the user submits a query.  
* A loading indicator that is conditionally displayed whenever the chat.status is 'loading', providing visual feedback to the user that the AI is processing their request.

### **Phase 4: Internationalization (i18n) and Polish**

**Objective:** To fully integrate internationalization (i18n) into the React frontend, enabling all user-facing text to be translated into multiple languages. This includes implementing a user-controlled language switcher and persisting the user's language preference.

**Key Technologies:** react-i18next, i18next, i18next-http-backend.

#### **Configuration and Scalable Translation Structure**

The foundation of the i18n system will be the react-i18next and i18next libraries.28 A critical aspect of a production-ready i18n setup is managing translation files in a scalable way. Placing all translations into a single, monolithic JSON object inside the main configuration file is not maintainable.28

Instead, a more organized approach using i18next namespaces will be adopted. Translation resources will be structured in the public/locales/ directory, organized by language and then by feature or page. This modular structure makes it easier for translators to work on specific parts of the application without being overwhelmed and simplifies maintenance.30

Example structure:

public/  
└── locales/  
    ├── en/  
    │   ├── common.json     \# { "submit": "Submit", "cancel": "Cancel" }  
    │   ├── chat.json       \# { "sendMessage": "Send Message", "placeholder": "Ask about metabolomics..." }  
    │   └── account.json    \# { "pageTitle": "Your Account", "preferredLanguage": "Preferred Language" }  
    └── es/  
        ├── common.json  
        ├── chat.json  
        └── account.json

The i18next-http-backend plugin will be configured to lazy-load these JSON files as needed, improving initial application load time.

#### **Implementation in Components**

* The root \<App /\> component will be wrapped in the I18nextProvider to make the i18n instance available throughout the component tree.  
* In functional components, the useTranslation hook will be the primary tool for accessing the translation function (t) and the i18n instance.28  
* All hardcoded strings in the UI will be replaced with calls to the t function, using keys that correspond to the structure in the JSON files (e.g., t('chat:sendMessage')).  
* For more complex translations that need to include JSX elements (like links or bold text), the \<Trans\> component will be used, which allows for embedding components within translated strings.28

#### **Language Switcher and User Preference**

A LanguageSwitcher component (e.g., a dropdown menu) will be created. When a user selects a new language, this component will call i18n.changeLanguage(newLang) to update the UI in real-time.

This feature will be integrated with the user account system. When a logged-in user changes the language, an API call will be dispatched to a PATCH /users/me endpoint to update and persist their preferred\_language in the database. Upon subsequent logins, the application will read this preference from the user's profile and initialize i18next with the correct language, ensuring a consistent experience.

### **Phase 5: Containerization and CI/CD for the MVP**

**Objective:** To create a reproducible, production-like local development environment using Docker and Docker Compose. This phase will also establish a complete Continuous Integration and Continuous Deployment (CI/CD) pipeline with GitHub Actions to automate the testing and deployment of the MVP.

**Key Technologies:** Docker, Docker Compose, Nginx, GitHub Actions.

#### **Dockerization**

The entire application stack will be containerized to ensure consistency across development, staging, and production environments.

* **Backend Dockerfile:** A multi-stage Dockerfile will be created for the FastAPI application. The first build stage will install Python dependencies into a virtual environment, leveraging Docker's layer caching to speed up subsequent builds. The final, lean production stage will copy the application code and the prepared virtual environment, and use Gunicorn with Uvicorn workers as the entry point, which is the standard, battle-tested way to run FastAPI in production.32  
* **Frontend Dockerfile:** A multi-stage Dockerfile will also be used for the React frontend. The build stage will use a Node.js image to install dependencies (npm install) and create a production build of the static assets (npm run build). The final stage will use a lightweight Nginx image and simply copy the static files (HTML, CSS, JS) from the build stage into Nginx's web root directory.33  
* **Nginx Reverse Proxy:** An nginx.conf file will be configured to act as the single entry point to the application. Nginx will be responsible for two key tasks:  
  1. Serving the static React application files for any requests to the root (/) and other frontend routes.  
  2. Acting as a reverse proxy, forwarding any requests with a path starting with /api/ to the FastAPI backend container. This is a standard and robust pattern for deploying a single-page application (SPA) with its API backend.34

#### **Docker Compose Orchestration**

A docker-compose.yml file will orchestrate the entire stack, defining the services and their relationships. This allows the entire multi-container application to be launched with a single command (docker-compose up). The compose file will define four services:

1. db: A PostgreSQL service, using an official image and a Docker volume to persist database data across restarts.  
2. redis: A Redis service for the JWT blocklist.  
3. backend: The FastAPI application, built from its Dockerfile.  
4. frontend: The Nginx server with the React app's static files, built from its Dockerfile. It will expose port 80 to the host machine.

Environment variables for database credentials, JWT secrets, and other configurations will be passed into the containers securely using .env files.33

#### **CI/CD Pipeline with GitHub Actions**

To enforce code quality and automate deployments, a two-workflow strategy will be implemented using GitHub Actions. Separating CI and CD concerns makes the pipelines more manageable and focused.37

* **ci.yml (Continuous Integration):** This workflow will be triggered on every pull\_request targeting the main branch. It will run parallel jobs for the backend and frontend to ensure rapid feedback.  
  * **Backend Job:** Install dependencies, run a linter (e.g., flake8), and execute the test suite with pytest.  
  * Frontend Job: Install dependencies, run a linter (e.g., eslint), and execute unit/component tests with vitest.  
    Pull requests will be blocked from merging until all CI checks pass, guaranteeing the stability of the main branch.38  
* **cd.yml (Continuous Deployment):** This workflow will be triggered automatically on every push or merge to the main branch. It will execute the following steps:  
  1. **Authenticate:** Log in to a container registry (e.g., Docker Hub, AWS ECR, GitHub Container Registry).  
  2. **Build & Push:** Build the backend and frontend Docker images, tag them with a unique identifier (e.g., the Git commit SHA), and push them to the registry.  
  3. **Deploy:** Securely SSH into the production server using credentials stored in GitHub Secrets (e.g., SSH\_HOST, SSH\_USERNAME, SSH\_PRIVATE\_KEY).35  
  4. **Restart:** On the server, execute commands to pull the newly pushed Docker images and restart the application using docker-compose. For example: docker-compose pull && docker-compose up \-d \--force-recreate. This ensures a zero-downtime update if configured correctly with a load balancer, or a minimal-downtime update otherwise.

---

## **Stage 2: Expansion with Advanced Data Sources**

With the robust MVP architecture in place, Stage 2 focuses on elevating the Clinical Metabolomics Oracle from a simple LLM wrapper to a sophisticated, multi-modal reasoning system. This stage will introduce an intelligent orchestration layer and integrate diverse, specialized data sources, transforming the CMO into a powerful tool for scientific inquiry.

### **Phase 6: The Cognitive Core: Designing the Orchestration Layer**

**Objective:** To architect and implement a flexible, extensible orchestration layer within the backend. This layer will serve as the system's "brain," capable of intelligently planning query strategies and synthesizing information from multiple, heterogeneous data sources to formulate comprehensive answers.

**Key Technologies:** Python, Design Patterns (Strategy, Chain of Responsibility).

#### **Architectural Vision: The Planner-Executor Model**

A simple, hardcoded sequence of data source lookups (e.g., always check RAG, then Graph, then Web) is brittle and inefficient. It fails to adapt to the user's intent. For instance, a query like *"What is the latest research on metformin's effect on glycolysis?"* should prioritize a web search and a knowledge graph query, while *"Summarize this uploaded protocol"* should exclusively use the RAG system.

To achieve true intelligent behavior, the orchestration layer will be built on a **Planner-Executor** model. This is a more advanced pattern that mimics a reasoning process.40

1. **The Planner:** This module is the strategic component. When a user query is received, the Planner's first step is to formulate a plan of action. It will use a targeted call to a fast and efficient LLM (like GPT-3.5-Turbo or a specialized fine-tuned model). The prompt for this call will be carefully engineered to act as a "tool selection" prompt. It will describe the available data source "tools" (e.g., scientific\_paper\_rag, metabolic\_pathway\_graph, live\_web\_search, general\_knowledge\_qa) and their specific capabilities. The LLM's task is to analyze the user's query and return a structured JSON object representing a multi-step execution plan. For example, for the query about metformin, the plan might look like this:  
   JSON  
   {  
     "plan": \[  
       {  
         "step": 1,  
         "tool": "live\_web\_search",  
         "query": "latest research metformin glycolysis 2024",  
         "justification": "Query asks for the 'latest research', which requires real-time web access."  
       },  
       {  
         "step": 2,  
         "tool": "metabolic\_pathway\_graph",  
         "query": "Find pathways connecting Metformin and Glycolysis",  
         "justification": "Query mentions specific biochemical entities and pathways, suitable for the knowledge graph."  
       }  
     \]  
   }

2. **The Executor:** This module is the tactical component. It receives the execution plan from the Planner and carries it out step-by-step. It will iterate through the plan, invoking the specified tool with the specified query for each step and collecting the results (the "evidence").

#### **Modular Tools with the Strategy Design Pattern**

To make the Executor flexible and extensible, the various data source tools will be implemented using the **Strategy design pattern**.41 This pattern allows for defining a family of algorithms, encapsulating each one, and making them interchangeable.

* An abstract base class, DataSourceStrategy, will define a common interface:  
  Python  
  \# app/core/orchestrator/strategies.py  
  from abc import ABC, abstractmethod

  class DataSourceStrategy(ABC):  
      @abstractmethod  
      async def execute(self, query: str) \-\> dict:  
          """Executes a query against the data source and returns the evidence."""  
          pass

* Concrete classes will implement this interface for each data source: RagStrategy, GraphStrategy, WebSearchStrategy, and OpenAIAssistantStrategy.  
* The Executor will maintain a registry (a dictionary) mapping the tool names from the Planner (e.g., "live\_web\_search") to instances of these strategy classes. This makes the system highly modular. To add a new data source in the future, a developer only needs to create a new strategy class and update the Planner's prompt to make it aware of the new tool.

#### **The Synthesis Step**

After the Executor has gathered all the evidence from the executed plan, the final step is synthesis. A Synthesizer module will collate all the retrieved contexts, the original user query, and a directive prompt. This entire package will be sent to a powerful, high-quality LLM (e.g., GPT-4-Turbo). The model's task will be to synthesize a single, coherent, and well-supported answer for the user, incorporating citations and evidence from the various sources it was provided. This final answer is what will be streamed back to the user.

### **Phase 7: Integrating Multi-Modal Data Sources**

**Objective:** To implement the concrete data source strategies defined in the previous phase. This involves setting up the necessary infrastructure for the vector and graph databases and building the data ingestion pipelines for each.

**Key Technologies:** Qdrant, Milvus, LangChain, LlamaIndex, Neo4j, Perplexity API.

#### **Sub-Phase 7.1: Traditional RAG with a Vector Database**

* **Technology Selection:** A dedicated vector database is essential for efficient similarity search in a RAG system. While there are many options like Pinecone and Milvus 42,  
  **Qdrant** is selected for this project. It is an open-source, high-performance vector database written in Rust, specifically noted for its advanced filtering capabilities and optimizations for RAG workflows.43 Qdrant will be added as a new service to the project's  
  docker-compose.yml.  
* **Ingestion Pipeline and Content-Aware Chunking:** A robust ingestion pipeline is the backbone of any RAG system. This will be implemented as a background task or a separate microservice. A crucial element of this pipeline is the chunking strategy. Simple fixed-size chunking is inadequate for complex scientific documents, as it can arbitrarily split tables, figures, and coherent arguments, destroying semantic context.44  
  Therefore, a **content-aware chunking** strategy will be implemented.45 The ingestion pipeline will first parse the structure of the uploaded documents (e.g., PDFs of clinical trial results or lab protocols). It will identify logical sections like the abstract, introduction, methods, results, and individual paragraphs. Chunking will occur along these logical boundaries, ensuring that each chunk represents a semantically coherent unit of information.46 Tables will be specifically identified, converted to a structured format like Markdown, and stored as distinct chunks with metadata linking them back to their position in the original document. This preserves their informational integrity.  
* **Implementation of RagStrategy:** The RagStrategy class will implement the execute method. It will take the query from the orchestrator, use a sentence-transformer model to create a vector embedding of the query, and then query the Qdrant database to retrieve the top-k most semantically similar document chunks. The text content of these chunks will be returned as evidence.

#### **Sub-Phase 7.2: GraphRAG with a Neo4j Knowledge Graph**

The knowledge graph provides the CMO with a structured "understanding" of the relationships between biological entities, enabling precise answers to complex relational questions.

* **Neo4j Metabolomics Graph Schema:** A well-designed schema is the key to an effective knowledge graph. The following schema defines the core entities and relationships for the CMO, based on established biological ontologies like KEGG and HMDB.47

| Node Label | Description | Key Properties |
| :---- | :---- | :---- |
| Metabolite | A small molecule involved in metabolism. | name, hmdb\_id, kegg\_id, formula, iupac\_name |
| Protein | A protein, which may function as an enzyme. | name, uniprot\_id, gene\_symbol |
| Reaction | A specific biochemical reaction. | name, kegg\_id, ec\_number, is\_reversible |
| Pathway | A series of connected biochemical reactions. | name, kegg\_id, description |
| Disease | A clinical condition or disease. | name, omim\_id, description |
| Gene | A gene that codes for a protein. | name, entrez\_id, symbol |
| Article | A scientific publication. | pmid, title, journal, year |

| Relationship Type | Source \-\> Target | Description | Properties |
| :---- | :---- | :---- | :---- |
| IS\_A | Metabolite \-\> Metabolite | Represents class hierarchy (e.g., Alanine IS\_A Amino Acid). |  |
| CONSUMES | Reaction \-\> Metabolite | The reaction uses the metabolite as a substrate. | stoichiometry |
| PRODUCES | Reaction \-\> Metabolite | The reaction generates the metabolite as a product. | stoichiometry |
| CATALYZES | Protein \-\> Reaction | The protein acts as an enzyme for the reaction. |  |
| CODES\_FOR | Gene \-\> Protein | The gene encodes the protein. |  |
| PART\_OF | Reaction \-\> Pathway | The reaction is a step in the pathway. |  |
| ASSOCIATED\_WITH | Metabolite \-\> Disease | The metabolite is linked to the disease. | evidence\_level, source\_pmid |
| MENTIONS | Article \-\> Metabolite | The article discusses the metabolite. | context |

* **Data Ingestion and Schema Optimization:** An ingestion pipeline will be developed to parse data from standard biological formats like Systems Biology Markup Language (SBML) 49 and public databases (KEGG, HMDB, PubMed). To optimize query performance for common questions, the ingestion process will create "shortcut" relationships. For example, in addition to linking a  
  Metabolite to a Reaction and that Reaction to a Pathway, a direct \`\` relationship will be created between the Metabolite and the Pathway. This denormalization significantly speeds up queries that ask about a metabolite's role in broader pathways.50  
* **Implementation of GraphStrategy:** This strategy will be responsible for converting natural language questions into formal Cypher queries. Initially, this can be achieved with keyword mapping and template-based query generation. For more advanced capability, an LLM can be prompted to generate Cypher queries directly. The strategy will execute the query against the Neo4j database (running in its own Docker container) and return the structured results as evidence for the synthesizer.52

#### **Sub-Phase 7.3: Live Web Search with Perplexity API**

* **API Integration:** The Perplexity API is chosen for live web search because it is designed for conversational AI and provides up-to-date, cited information.54 Its API is OpenAI-compatible, which simplifies integration.55 The API key will be securely stored and managed via the Admin Panel.  
* **Implementation of WebSearchStrategy:** This class will take a user's query, format it for the Perplexity API, and make a call to one of its online models (e.g., sonar-medium-online). The response, including the generated text and the source citations, will be returned as evidence.

### **Phase 8: Enhancing the UI with Transparent Reasoning**

**Objective:** To upgrade the chat UI to provide users with real-time visibility into the orchestrator's reasoning process. This transparency builds trust and provides a more engaging and informative user experience.

**Key Technologies:** React, Redux Toolkit, EventSource API.

#### **Backend: Streaming Status Updates**

The /chat streaming endpoint in FastAPI will be modified. As the Orchestration Layer executes its plan, it will now yield status updates in addition to the final answer tokens. These updates will be sent as distinct SSE events.

Example of yielded data stream:

data: {"type": "status", "message": "Planning query strategy..."}\\n\\n  
data: {"type": "status", "message": "Querying knowledge graph for metabolic pathways..."}\\n\\n  
data: {"type": "status", "message": "Searching web for recent publications..."}\\n\\n  
data: {"type": "status", "message": "Synthesizing answer..."}\\n\\n  
data: {"type": "token", "content": "Based "}\\n\\n  
data: {"type": "token", "content": "on the "}\\n\\n  
...

#### **Frontend: Differentiated Event Handling**

The frontend's EventSource handler in the ChatPage component will be updated to differentiate between these event types.

* A new piece of state, currentStatus: string | null, will be added to the Redux chat slice.  
* The onmessage event listener will parse the incoming JSON data from the stream.  
  * If data.type \=== 'status', it will dispatch a setChatStatus(data.message) action, updating the status message displayed in the UI.  
  * If data.type \=== 'token', it will dispatch the appendAiMessageChunk(data.content) action as before, building the final response.  
* When the stream ends, a final action will be dispatched to clear the status message (e.g., setChatStatus(null)).

This implements the decoupled stream handling architecture designed in Phase 3, now enhanced for richer, multi-type events.

#### **UI Component for Status Display**

A new StatusDisplay component will be created within the chat interface. It will be conditionally rendered whenever chat.status \=== 'loading' and chat.currentStatus is not null. This component will display the current working status (e.g., "Querying Knowledge Graph...") to the user, providing a transparent window into the AI's process.

### **Phase 9: The Admin Panel: Control and Configuration**

**Objective:** To build a secure, role-protected administrative interface that provides authorized personnel with the tools to manage users, data sources, and the configuration of the core orchestration layer.

**Key Technologies:** React, React Router DOM, Redux Toolkit, FastAPI.

#### **Secure Admin Area and Endpoints**

Access to the admin panel will be strictly controlled. The frontend routes under /admin/\* are already protected by the AdminRouteLayout, which verifies that the logged-in user has the 'admin' role. On the backend, all API endpoints prefixed with /api/v1/admin/\* will be protected by the get\_current\_admin\_user dependency, ensuring only authenticated administrators can access them.

#### **Admin Panel Features**

The admin panel will be a dedicated section of the website providing the following functionalities:

* **User Management:** A dashboard to view a list of all registered users. Admins will have the ability to change a user's role (e.g., promote to admin) or delete a user account. This will be powered by new, admin-only API endpoints: GET /admin/users, PATCH /admin/users/{user\_id}, and DELETE /admin/users/{user\_id}.  
* **RAG Document Management:** An interface for managing the content of the traditional RAG system. This will include:  
  * A file upload form to submit new documents (e.g., PDFs of scientific articles) for ingestion.  
  * A table displaying the status of all ingested documents (e.g., "Processing," "Completed," "Error").  
  * An option to delete a document and its associated vectors from the system.  
    This functionality will be supported by endpoints like POST /admin/rag/documents, GET /admin/rag/documents, and DELETE /admin/rag/documents/{doc\_id}. The POST endpoint will trigger the asynchronous ingestion and chunking pipeline.  
* **Knowledge Graph Management:** A simple control panel with buttons to trigger backend maintenance scripts for the Neo4j database. This allows admins to safely initiate tasks like a full data refresh from source databases (e.g., KEGG) or run data integrity checks without needing direct database access. This is powered by a POST /admin/kg/manage endpoint.  
* **Orchestrator Configuration:** A form to dynamically tune the behavior of the reasoning engine without requiring code changes. This is a powerful feature for system optimization. The configuration will be defined by a clear schema and managed via GET /admin/orchestrator/config and PUT /admin/orchestrator/config endpoints.

#### **Orchestrator Configuration Schema**

The following table defines the tunable parameters that will be exposed in the admin panel.

| Parameter Name | Data Type | UI Control | Description | Default Value |
| :---- | :---- | :---- | :---- | :---- |
| perplexity\_api\_key | string | Password Input | The API key for accessing the Perplexity API. | "" |
| planner\_model | string | Dropdown | The LLM to use for the Planner module. | gpt-3.5-turbo |
| synthesizer\_model | string | Dropdown | The LLM to use for the final Synthesizer module. | gpt-4-turbo |
| rag\_top\_k | integer | Number Input | The number of document chunks to retrieve from the vector DB. | 5 |
| graph\_query\_limit | integer | Number Input | The maximum number of results to return from a Cypher query. | 10 |
| enable\_web\_search | boolean | Toggle Switch | Globally enables or disables the live web search tool. | true |
| source\_priority\_order | array\[string\] | Draggable List | Defines the default priority of evidence for the synthesizer. | \["graph", "rag", "web"\] |

### **Phase 10: Comprehensive System Testing and Final Deployment**

**Objective:** To conduct thorough end-to-end testing of the fully integrated, advanced system and to update the CI/CD pipeline to manage the complete production stack, including the new data services.

**Key Technologies:** Pytest, Vitest, Cypress, Docker, GitHub Actions.

#### **Expanded Testing Strategy**

The existing testing framework will be expanded to ensure full coverage of the new, complex functionalities.

* **Unit and Integration Tests:** The pytest (backend) and vitest (frontend) suites will be augmented. This includes writing tests for the Orchestrator's Planner and Executor logic, each data source strategy (mocking the external APIs and databases), and all admin panel API endpoints and UI components.  
* **End-to-End (E2E) Testing:** A dedicated E2E testing framework, **Cypress**, will be introduced. Cypress tests simulate real user journeys from start to finish in a browser environment. Key E2E test scenarios will include:  
  * **Complex Query Journey:** A user logs in, asks a question known to trigger multiple data sources (e.g., "What is the role of Alanine in the urea cycle and are there any recent clinical trials?"), verifies that the correct status updates are displayed in sequence, and confirms that the final streamed answer is coherent and contains citations.  
  * **Admin Journey:** An admin user logs in, navigates to the admin panel, successfully uploads a new document for the RAG system, and verifies that its status appears as "Processing" in the management table.

#### **Updating the Deployment Pipeline**

The production deployment process must be updated to include the new data services.

* A docker-compose.prod.yml file will be created or updated to define the full production stack, now including the qdrant and neo4j services alongside the existing backend, frontend, db, and redis services.  
* The cd.yml GitHub Actions workflow will be modified. The final deployment step, which runs on the production server, will now use this production-specific compose file (e.g., docker-compose \-f docker-compose.prod.yml up \-d).  
* All new required environment variables and secrets (e.g., Neo4j authentication, Perplexity API key) will be added to GitHub Actions secrets and securely passed to the deployment environment.

#### **Final Review and Go-Live**

Before the final launch of the advanced system, a final series of checks will be performed:

1. **Security Audit:** A comprehensive review of the application for common web vulnerabilities (e.g., SQL injection, Cross-Site Scripting (XSS), insecure direct object references in the admin panel).  
2. **Performance Testing:** Load testing will be conducted on a staging environment that mirrors production to identify and resolve any performance bottlenecks in the orchestration layer or data source queries.  
3. **Deployment:** The final, automated deployment will be executed by merging the code into the main branch.  
4. **Post-Launch Monitoring:** System logs, application performance metrics, and resource utilization for all services will be closely monitored to ensure stability and performance in the live environment.

#### **Works cited**

1. Securing FastAPI with JWT Token-based Authentication \- TestDriven.io, accessed June 15, 2025, [https://testdriven.io/blog/fastapi-jwt-auth/](https://testdriven.io/blog/fastapi-jwt-auth/)  
2. Flawless authentication with FastAPI and JSON Web Tokens \- Opcito, accessed June 15, 2025, [https://www.opcito.com/blogs/flawless-authentication-with-fastapi-and-json-web-tokens](https://www.opcito.com/blogs/flawless-authentication-with-fastapi-and-json-web-tokens)  
3. SQL (Relational) Databases \- FastAPI, accessed June 15, 2025, [https://fastapi.tiangolo.com/tutorial/sql-databases/](https://fastapi.tiangolo.com/tutorial/sql-databases/)  
4. Asynchronous Database Sessions in FastAPI with SQLAlchemy \- DEV Community, accessed June 15, 2025, [https://dev.to/akarshan/asynchronous-database-sessions-in-fastapi-with-sqlalchemy-1o7e](https://dev.to/akarshan/asynchronous-database-sessions-in-fastapi-with-sqlalchemy-1o7e)  
5. From Zero to Production: Setting Up a SQL Database with Async Engine in FastAPI, accessed June 15, 2025, [https://timothy.hashnode.dev/from-zero-to-production-setting-up-a-sql-database-with-async-engine-in-fastapi](https://timothy.hashnode.dev/from-zero-to-production-setting-up-a-sql-database-with-async-engine-in-fastapi)  
6. polymorphisma/fastapi-alembic \- GitHub, accessed June 15, 2025, [https://github.com/polymorphisma/fastapi-alembic](https://github.com/polymorphisma/fastapi-alembic)  
7. Setup FastAPI Project with Async SQLAlchemy 2, Alembic, PostgreSQL and Docker, accessed June 15, 2025, [https://berkkaraal.com/blog/2024/09/19/setup-fastapi-project-with-async-sqlalchemy-2-alembic-postgresql-and-docker/](https://berkkaraal.com/blog/2024/09/19/setup-fastapi-project-with-async-sqlalchemy-2-alembic-postgresql-and-docker/)  
8. FastAPI SQLAlchemy 2, Alembic and PostgreSQL Setup Tutorial 2025 \- YouTube, accessed June 15, 2025, [https://www.youtube.com/watch?v=gg7AX1iRnmg](https://www.youtube.com/watch?v=gg7AX1iRnmg)  
9. FastAPI / SQLAlchemy example project? : r/learnpython \- Reddit, accessed June 15, 2025, [https://www.reddit.com/r/learnpython/comments/1b9i0kq/fastapi\_sqlalchemy\_example\_project/](https://www.reddit.com/r/learnpython/comments/1b9i0kq/fastapi_sqlalchemy_example_project/)  
10. FastAPI Security With JWT Authentication \- Ficode, accessed June 15, 2025, [https://www.ficode.com/blog/everything-you-need-to-know-about-fastapi-security-with-jwt](https://www.ficode.com/blog/everything-you-need-to-know-about-fastapi-security-with-jwt)  
11. Refresh Tokens \- FastAPI JWT Auth, accessed June 15, 2025, [https://indominusbyte.github.io/fastapi-jwt-auth/usage/refresh/](https://indominusbyte.github.io/fastapi-jwt-auth/usage/refresh/)  
12. JWT Authentication (Renew User Access Using Refresh Tokens) \- FastAPI Beyond CRUD (Part 11\) \- YouTube, accessed June 15, 2025, [https://www.youtube.com/watch?v=JitVZm8rfks](https://www.youtube.com/watch?v=JitVZm8rfks)  
13. JWT Tokens \- Fastapi Framework Documentation, accessed June 15, 2025, [https://tert0.github.io/fastapi-framework/jwt/jwt\_tokens/](https://tert0.github.io/fastapi-framework/jwt/jwt_tokens/)  
14. OAuth2 Example | Logout and Refresh Token : r/FastAPI \- Reddit, accessed June 15, 2025, [https://www.reddit.com/r/FastAPI/comments/1fed43y/oauth2\_example\_logout\_and\_refresh\_token/](https://www.reddit.com/r/FastAPI/comments/1fed43y/oauth2_example_logout_and_refresh_token/)  
15. FastAPI with SQLAlchemy Tutorial \- YouTube, accessed June 15, 2025, [https://www.youtube.com/watch?v=8GSYx-KDEas](https://www.youtube.com/watch?v=8GSYx-KDEas)  
16. Getting Started \- Vite, accessed June 15, 2025, [https://vite.dev/guide/](https://vite.dev/guide/)  
17. A beginners guide to using Vite React \- CodeParrot, accessed June 15, 2025, [https://codeparrot.ai/blogs/a-beginners-guide-to-using-vite-react](https://codeparrot.ai/blogs/a-beginners-guide-to-using-vite-react)  
18. Advanced Guide to Using Vite with React in 2025 \- CodeParrot, accessed June 15, 2025, [https://codeparrot.ai/blogs/advanced-guide-to-using-vite-with-react-in-2025](https://codeparrot.ai/blogs/advanced-guide-to-using-vite-with-react-in-2025)  
19. Redux Toolkit \- GeeksforGeeks, accessed June 15, 2025, [https://www.geeksforgeeks.org/reactjs/redux-toolkit/](https://www.geeksforgeeks.org/reactjs/redux-toolkit/)  
20. Redux: Async Actions with Middleware and Thunks Cheatsheet | Codecademy, accessed June 15, 2025, [https://www.codecademy.com/learn/fscp-22-redux/modules/wdcp-22-async-actions-with-middleware-and-thunks/cheatsheet](https://www.codecademy.com/learn/fscp-22-redux/modules/wdcp-22-async-actions-with-middleware-and-thunks/cheatsheet)  
21. Authentication with React Router v6: A complete guide \- LogRocket Blog, accessed June 15, 2025, [https://blog.logrocket.com/authentication-react-router-v6/](https://blog.logrocket.com/authentication-react-router-v6/)  
22. React Router 7: Private Routes (alias Protected Routes) \- Robin Wieruch, accessed June 15, 2025, [https://www.robinwieruch.de/react-router-private-routes/](https://www.robinwieruch.de/react-router-private-routes/)  
23. Exploring the Universe of Nested and Protected Routes with React Router v6 \- CodeSignal, accessed June 15, 2025, [https://codesignal.com/learn/courses/routing-in-react-applications/lessons/exploring-the-universe-of-nested-and-protected-routes-with-react-router-v6](https://codesignal.com/learn/courses/routing-in-react-applications/lessons/exploring-the-universe-of-nested-and-protected-routes-with-react-router-v6)  
24. Guide to configure protected routes in react-router-dom v6 \- GitHub, accessed June 15, 2025, [https://github.com/Standard-IO/protected-routes-react](https://github.com/Standard-IO/protected-routes-react)  
25. createAsyncThunk \- Redux Toolkit, accessed June 15, 2025, [https://redux-toolkit.js.org/api/createAsyncThunk](https://redux-toolkit.js.org/api/createAsyncThunk)  
26. Using Redux Toolkit's createAsyncThunk \- LogRocket Blog, accessed June 15, 2025, [https://blog.logrocket.com/using-redux-toolkits-createasyncthunk/](https://blog.logrocket.com/using-redux-toolkits-createasyncthunk/)  
27. Redux Essentials, Part 5: Async Logic and Data Fetching, accessed June 15, 2025, [https://redux.js.org/tutorials/essentials/part-5-async-logic](https://redux.js.org/tutorials/essentials/part-5-async-logic)  
28. Quick start \- react-i18next documentation, accessed June 15, 2025, [https://react.i18next.com/guides/quick-start](https://react.i18next.com/guides/quick-start)  
29. Getting started \- react-i18next documentation, accessed June 15, 2025, [https://react.i18next.com/getting-started](https://react.i18next.com/getting-started)  
30. React i18next (Complete Tutorial) \- YouTube, accessed June 15, 2025, [https://www.youtube.com/watch?v=U4\_P\_l3L\_EA](https://www.youtube.com/watch?v=U4_P_l3L_EA)  
31. React Multi Language App \- i18next Tutorial with React JS \- YouTube, accessed June 15, 2025, [https://www.youtube.com/watch?v=dltHi9GWMIo\&pp=0gcJCdgAo7VqN5tD](https://www.youtube.com/watch?v=dltHi9GWMIo&pp=0gcJCdgAo7VqN5tD)  
32. FastAPI in Containers \- Docker, accessed June 15, 2025, [https://fastapi.tiangolo.com/deployment/docker/](https://fastapi.tiangolo.com/deployment/docker/)  
33. Happily-Coding/FastapiReactNginxDocker: A minimalistic Fastapi React Nginx Docker web app example. \- GitHub, accessed June 15, 2025, [https://github.com/Happily-Coding/FastapiReactNginxDocker](https://github.com/Happily-Coding/FastapiReactNginxDocker)  
34. How to deploy React FastAPI Containerized Application : r/docker \- Reddit, accessed June 15, 2025, [https://www.reddit.com/r/docker/comments/19diqj6/how\_to\_deploy\_react\_fastapi\_containerized/](https://www.reddit.com/r/docker/comments/19diqj6/how_to_deploy_react_fastapi_containerized/)  
35. Deploying a FastAPI Application with CI/CD Pipeline: HNG Task 2 \- DEV Community, accessed June 15, 2025, [https://dev.to/dipe\_/deploying-a-fastapi-application-with-cicd-pipeline-hng-task-3-5598](https://dev.to/dipe_/deploying-a-fastapi-application-with-cicd-pipeline-hng-task-3-5598)  
36. FastAPI \+ React \+ Docker \+ Nginx \- GitHub, accessed June 15, 2025, [https://github.com/vikramgulia/fastapi-react](https://github.com/vikramgulia/fastapi-react)  
37. Deploying a FastAPI App with CI/CD: GitHub Actions, Docker, Nginx & AWS EC2, accessed June 15, 2025, [https://dev.to/tony\_uketui\_6cca68c7eba02/deploying-a-fastapi-app-with-cicd-github-actions-docker-nginx-aws-ec2-6p8](https://dev.to/tony_uketui_6cca68c7eba02/deploying-a-fastapi-app-with-cicd-github-actions-docker-nginx-aws-ec2-6p8)  
38. Setting Up GitHub Actions CI for FastAPI: Intro to Taskfile and Pre-Jobs \- PyImageSearch, accessed June 15, 2025, [https://pyimagesearch.com/2024/10/07/setting-up-github-actions-ci-for-fastapi-intro-to-taskfile-and-pre-jobs/](https://pyimagesearch.com/2024/10/07/setting-up-github-actions-ci-for-fastapi-intro-to-taskfile-and-pre-jobs/)  
39. A demonstration of how to use FastAPI together with React \- GitHub, accessed June 15, 2025, [https://github.com/finsberg/fastapi-react-app](https://github.com/finsberg/fastapi-react-app)  
40. Orchestration Layer: Coordinating Data for Seamless Integration \- Acceldata, accessed June 15, 2025, [https://www.acceldata.io/blog/orchestration-layer-explained-functions-tools-and-best-practices](https://www.acceldata.io/blog/orchestration-layer-explained-functions-tools-and-best-practices)  
41. Design Patterns in Python \- Refactoring.Guru, accessed June 15, 2025, [https://refactoring.guru/design-patterns/python](https://refactoring.guru/design-patterns/python)  
42. Milvus vs Pinecone \- Zilliz, accessed June 15, 2025, [https://zilliz.com/comparison/milvus-vs-pinecone](https://zilliz.com/comparison/milvus-vs-pinecone)  
43. Best Vector Database for RAG : r/vectordatabase \- Reddit, accessed June 15, 2025, [https://www.reddit.com/r/vectordatabase/comments/1hzovpy/best\_vector\_database\_for\_rag/](https://www.reddit.com/r/vectordatabase/comments/1hzovpy/best_vector_database_for_rag/)  
44. 7 Chunking Strategies in RAG You Need To Know \- F22 Labs, accessed June 15, 2025, [https://www.f22labs.com/blogs/7-chunking-strategies-in-rag-you-need-to-know/](https://www.f22labs.com/blogs/7-chunking-strategies-in-rag-you-need-to-know/)  
45. 15 Chunking Techniques to Build Exceptional RAGs Systems \- Analytics Vidhya, accessed June 15, 2025, [https://www.analyticsvidhya.com/blog/2024/10/chunking-techniques-to-build-exceptional-rag-systems/](https://www.analyticsvidhya.com/blog/2024/10/chunking-techniques-to-build-exceptional-rag-systems/)  
46. A Guide to Chunking Strategies for Retrieval Augmented Generation (RAG) \- Sagacify, accessed June 15, 2025, [https://www.sagacify.com/news/a-guide-to-chunking-strategies-for-retrieval-augmented-generation-rag](https://www.sagacify.com/news/a-guide-to-chunking-strategies-for-retrieval-augmented-generation-rag)  
47. KODA: Agentic Framework for Microbiome Drug Target Discovery \- bioRxiv, accessed June 15, 2025, [https://www.biorxiv.org/content/10.1101/2025.05.27.656480v1.full.pdf](https://www.biorxiv.org/content/10.1101/2025.05.27.656480v1.full.pdf)  
48. Human Metabolome Database (HMDB), accessed June 15, 2025, [https://www.hmdb.ca/](https://www.hmdb.ca/)  
49. neo4jsbml: import systems biology markup language data into the graph database Neo4j | Request PDF \- ResearchGate, accessed June 15, 2025, [https://www.researchgate.net/publication/377451045\_neo4jsbml\_import\_systems\_biology\_markup\_language\_data\_into\_the\_graph\_database\_Neo4j](https://www.researchgate.net/publication/377451045_neo4jsbml_import_systems_biology_markup_language_data_into_the_graph_database_Neo4j)  
50. Recon2Neo4j: applying graph database technologies for managing comprehensive genome-scale networks \- PMC, accessed June 15, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC5408918/](https://pmc.ncbi.nlm.nih.gov/articles/PMC5408918/)  
51. Tools – Metabolism Regulation – Connecting signalling and metabolic networks, accessed June 15, 2025, [https://metabolismregulation.github.io/tools/](https://metabolismregulation.github.io/tools/)  
52. Biological Networks with Graph Database \- DEV Community, accessed June 15, 2025, [https://dev.to/munmud/biological-networks-with-graph-database-6hk](https://dev.to/munmud/biological-networks-with-graph-database-6hk)  
53. Reactome querying using Cypher and Neo4j (A) Extracting the... | Download Scientific Diagram \- ResearchGate, accessed June 15, 2025, [https://www.researchgate.net/figure/Reactome-querying-using-Cypher-and-Neo4j-A-Extracting-the-reactions-pathways-involving\_fig5\_341874296](https://www.researchgate.net/figure/Reactome-querying-using-Cypher-and-Neo4j-A-Extracting-the-reactions-pathways-involving_fig5_341874296)  
54. Perplexity API Ultimate Guide | Zuplo Blog, accessed June 15, 2025, [https://zuplo.com/blog/2025/03/28/perplexity-api](https://zuplo.com/blog/2025/03/28/perplexity-api)  
55. How to use Perplexity AI API with, or without a Pro Account \- Apidog, accessed June 15, 2025, [https://apidog.com/blog/perplexity-ai-api/](https://apidog.com/blog/perplexity-ai-api/)