# Clinical Metabolomics Oracle - Environment Variables Example
# Copy this file to .env and fill in the actual values

# Application Settings
PROJECT_NAME="Clinical Metabolomics Oracle"
VERSION="1.0.0"
ENVIRONMENT="development"  # development, staging, production
DEBUG=true

# Security Settings
SECRET_KEY="your-secret-key-here"
JWT_SECRET_KEY="your-jwt-secret-key-here"
JWT_ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Settings
DATABASE_URL="postgresql+asyncpg://postgres:password@localhost:5432/cmo_db"
POSTGRES_SERVER="localhost"
POSTGRES_USER="postgres"
POSTGRES_PASSWORD="password"
POSTGRES_DB="cmo_db"
POSTGRES_PORT=5432

# Redis Settings
REDIS_URL="redis://localhost:6379/0"
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_DB=0

# OpenAI Settings
OPENAI_API_KEY="your-openai-api-key-here"
CMO_ASSISTANT_ID="your-assistant-id-here"

# CORS Settings (comma-separated list)
ALLOWED_ORIGINS="http://localhost:3000,http://localhost:5173"
