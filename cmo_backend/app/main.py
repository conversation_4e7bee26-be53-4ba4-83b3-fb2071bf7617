"""
Clinical Metabolomics Oracle (CMO) FastAPI Application

This is the main FastAPI application for the Clinical Metabolomics Oracle,
a sophisticated AI-powered system for clinical metabolomics research.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Import configuration
from app.core.config import settings

# Create FastAPI application instance
app = FastAPI(
    title="Clinical Metabolomics Oracle API",
    description="Advanced AI-powered system for clinical metabolomics research and analysis",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint for health check"""
    return {
        "message": "Clinical Metabolomics Oracle API",
        "version": "1.0.0",
        "status": "operational"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}
