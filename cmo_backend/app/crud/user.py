"""
CRUD operations for User model

This module provides Create, Read, Update, Delete operations for the User model
with password hashing and authentication utilities.
"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from passlib.context import CryptContext

from app.models.user import User, UserCreate, UserUpdate, UserInDB
from app.models.auth import ChangePassword


# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a plain password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash from plain password"""
    return pwd_context.hash(password)


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """Get user by email address"""
    result = await db.execute(select(User).where(User.email == email))
    return result.scalar_one_or_none()


async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
    """Get user by ID"""
    result = await db.execute(select(User).where(User.id == user_id))
    return result.scalar_one_or_none()


async def create_user(db: AsyncSession, user_create: UserCreate) -> User:
    """
    Create a new user with hashed password
    
    Args:
        db: Database session
        user_create: User creation data
        
    Returns:
        Created user instance
        
    Raises:
        ValueError: If user with email already exists
    """
    # Check if user already exists
    existing_user = await get_user_by_email(db, user_create.email)
    if existing_user:
        raise ValueError(f"User with email {user_create.email} already exists")
    
    # Hash the password
    hashed_password = get_password_hash(user_create.password)
    
    # Create user instance
    db_user = User(
        email=user_create.email,
        hashed_password=hashed_password,
        preferred_language=user_create.preferred_language,
    )
    
    # Add to database
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    
    return db_user


async def authenticate_user(db: AsyncSession, email: str, password: str) -> Optional[User]:
    """
    Authenticate user with email and password
    
    Args:
        db: Database session
        email: User email
        password: Plain password
        
    Returns:
        User instance if authentication successful, None otherwise
    """
    user = await get_user_by_email(db, email)
    if not user:
        return None
    
    if not verify_password(password, user.hashed_password):
        return None
    
    return user


async def update_user(db: AsyncSession, user_id: int, user_update: UserUpdate) -> Optional[User]:
    """
    Update user information
    
    Args:
        db: Database session
        user_id: User ID to update
        user_update: Update data
        
    Returns:
        Updated user instance or None if not found
    """
    user = await get_user_by_id(db, user_id)
    if not user:
        return None
    
    # Update fields that are provided
    update_data = user_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)
    
    await db.commit()
    await db.refresh(user)
    
    return user


async def change_user_password(
    db: AsyncSession, 
    user_id: int, 
    password_change: ChangePassword
) -> bool:
    """
    Change user password after verifying current password
    
    Args:
        db: Database session
        user_id: User ID
        password_change: Password change data
        
    Returns:
        True if password changed successfully, False otherwise
    """
    user = await get_user_by_id(db, user_id)
    if not user:
        return False
    
    # Verify current password
    if not verify_password(password_change.current_password, user.hashed_password):
        return False
    
    # Update password
    user.hashed_password = get_password_hash(password_change.new_password)
    await db.commit()
    
    return True


async def deactivate_user(db: AsyncSession, user_id: int) -> bool:
    """
    Deactivate user account
    
    Args:
        db: Database session
        user_id: User ID to deactivate
        
    Returns:
        True if user deactivated successfully, False if not found
    """
    user = await get_user_by_id(db, user_id)
    if not user:
        return False
    
    user.is_active = False
    await db.commit()
    
    return True
