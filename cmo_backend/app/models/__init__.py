"""
Models package for the Clinical Metabolomics Oracle API

This package contains all SQLModel models and schemas for the application.
"""

from .user import (
    User,
    UserBase,
    UserCreate,
    UserUpdate,
    UserPublic,
    UserInDB,
    UserRole,
)

from .chat import (
    ChatMessage,
    ChatThread,
    ChatMessageBase,
    ChatThreadBase,
    ChatMessageCreate,
    ChatMessagePublic,
    ChatThreadCreate,
    ChatThreadUpdate,
    ChatThreadPublic,
    ChatThreadWithMessages,
    SenderType,
)

from .auth import (
    Token,
    TokenData,
    RefreshToken,
    LoginRequest,
    PasswordReset,
    PasswordResetConfirm,
    ChangePassword,
)

__all__ = [
    # User models
    "User",
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserPublic",
    "UserInDB",
    "UserRole",
    # Chat models
    "ChatMessage",
    "ChatThread",
    "ChatMessageBase",
    "ChatThreadBase",
    "ChatMessageCreate",
    "ChatMessagePublic",
    "ChatThreadCreate",
    "ChatThreadUpdate",
    "ChatThreadPublic",
    "ChatThreadWithMessages",
    "SenderType",
    # Auth models
    "Token",
    "TokenData",
    "RefreshToken",
    "LoginRequest",
    "PasswordReset",
    "PasswordResetConfirm",
    "ChangePassword",
]