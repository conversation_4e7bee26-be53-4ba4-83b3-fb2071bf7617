"""
User models for the Clinical Metabolomics Oracle API

This module defines the User model and related schemas using SQLModel
for both database table definition and API serialization.
"""

from typing import Optional
from enum import Enum
from datetime import datetime
from sqlmodel import SQLModel, Field


class UserRole(str, Enum):
    """User role enumeration"""
    USER = "user"
    ADMIN = "admin"


class UserBase(SQLModel):
    """Base user model with common fields"""
    email: str = Field(unique=True, index=True, max_length=255)
    preferred_language: str = Field(default="en", max_length=10)


class User(UserBase, table=True):
    """User database model"""
    __tablename__ = "users"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    hashed_password: str = Field(max_length=255)
    role: UserRole = Field(default=UserRole.USER)
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)


class UserCreate(UserBase):
    """Schema for user creation"""
    password: str = Field(min_length=8, max_length=100)


class UserUpdate(SQLModel):
    """Schema for user updates"""
    preferred_language: Optional[str] = Field(default=None, max_length=10)
    is_active: Optional[bool] = Field(default=None)


class UserPublic(UserBase):
    """Public user schema (excludes sensitive information)"""
    id: int
    role: UserRole
    is_active: bool
    created_at: datetime


class UserInDB(UserBase):
    """User schema with database fields (for internal use)"""
    id: int
    hashed_password: str
    role: UserRole
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
