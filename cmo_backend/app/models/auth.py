"""
Authentication models for the Clinical Metabolomics Oracle API

This module defines authentication-related schemas for JWT tokens,
login requests, and authentication responses.
"""

from typing import Optional
from sqlmodel import SQLModel, Field


class Token(SQLModel):
    """JWT token response schema"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


class TokenData(SQLModel):
    """Token data schema for JWT payload"""
    user_id: Optional[int] = None
    email: Optional[str] = None
    role: Optional[str] = None


class RefreshToken(SQLModel):
    """Refresh token request schema"""
    refresh_token: str


class LoginRequest(SQLModel):
    """Login request schema"""
    email: str = Field(max_length=255)
    password: str = Field(max_length=100)


class PasswordReset(SQLModel):
    """Password reset request schema"""
    email: str = Field(max_length=255)


class PasswordResetConfirm(SQLModel):
    """Password reset confirmation schema"""
    token: str
    new_password: str = Field(min_length=8, max_length=100)


class ChangePassword(SQLModel):
    """Change password schema"""
    current_password: str = Field(max_length=100)
    new_password: str = Field(min_length=8, max_length=100)
