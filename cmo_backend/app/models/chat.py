"""
Chat models for the Clinical Metabolomics Oracle API

This module defines the ChatThread and ChatMessage models using SQLModel
for managing conversation history and message storage.
"""

from typing import Optional, List
from enum import Enum
from datetime import datetime
from sqlmodel import SQLModel, Field, Relationship


class SenderType(str, Enum):
    """Message sender type enumeration"""
    USER = "user"
    AI = "ai"


class ChatMessageBase(SQLModel):
    """Base chat message model with common fields"""
    content: str = Field(max_length=10000)
    sender: SenderType
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ChatMessage(ChatMessageBase, table=True):
    """Chat message database model"""
    __tablename__ = "chat_messages"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    thread_id: int = Field(foreign_key="chat_threads.id", index=True)
    
    # Relationship to chat thread
    thread: Optional["ChatThread"] = Relationship(back_populates="messages")


class ChatThreadBase(SQLModel):
    """Base chat thread model with common fields"""
    title: str = Field(max_length=255)
    created_at: datetime = Field(default_factory=datetime.utcnow)


class ChatThread(ChatThreadBase, table=True):
    """Chat thread database model"""
    __tablename__ = "chat_threads"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    updated_at: Optional[datetime] = Field(default=None)
    
    # Relationship to messages
    messages: List[ChatMessage] = Relationship(back_populates="thread")


class ChatMessageCreate(ChatMessageBase):
    """Schema for creating a new chat message"""
    thread_id: Optional[int] = Field(default=None)


class ChatMessagePublic(ChatMessageBase):
    """Public chat message schema"""
    id: int
    thread_id: int


class ChatThreadCreate(SQLModel):
    """Schema for creating a new chat thread"""
    title: str = Field(max_length=255)


class ChatThreadUpdate(SQLModel):
    """Schema for updating a chat thread"""
    title: Optional[str] = Field(default=None, max_length=255)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class ChatThreadPublic(ChatThreadBase):
    """Public chat thread schema"""
    id: int
    user_id: int
    updated_at: Optional[datetime] = None


class ChatThreadWithMessages(ChatThreadPublic):
    """Chat thread schema with messages included"""
    messages: List[ChatMessagePublic] = []
